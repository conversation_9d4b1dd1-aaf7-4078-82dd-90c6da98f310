<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体训练功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        .open-dialog-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .open-dialog-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI智能体训练功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">功能说明</div>
            <div class="description">
                本次优化为AI智能体训练对话框添加了以下新功能：
            </div>
            <ul class="feature-list">
                <li>在输入框上方添加了"问答训练"按钮</li>
                <li>点击按钮后自动插入问答模板："问题：\n\n回答：\n\n"</li>
                <li>输入框支持自适应高度，根据内容行数自动调整</li>
                <li>最大显示6行，超出后显示滚动条</li>
                <li>优化了用户体验和界面美观度</li>
            </ul>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试步骤</div>
            <div class="description">
                由于这是一个Tampermonkey脚本，需要在微信小店客服页面中测试。请按以下步骤操作：
            </div>
            <ol>
                <li>打开微信小店客服页面</li>
                <li>启动脚本，打开控制面板</li>
                <li>点击"AI智能体训练"按钮</li>
                <li>在训练对话框中测试新功能：
                    <ul>
                        <li>点击"问答训练"按钮</li>
                        <li>观察输入框是否自动插入模板</li>
                        <li>输入多行文本测试自适应高度</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="test-section">
            <div class="test-title">预期效果</div>
            <div class="description">
                <strong>问答训练按钮：</strong><br>
                • 位于输入框上方，居中显示<br>
                • 渐变蓝紫色背景，白色文字<br>
                • 点击后在输入框中插入："问题：\n\n回答：\n\n"<br>
                • 光标自动定位到"问题："后面<br><br>
                
                <strong>输入框自适应高度：</strong><br>
                • 默认高度为50px（1行）<br>
                • 根据内容自动调整高度<br>
                • 最大高度144px（6行）<br>
                • 超出6行时显示垂直滚动条<br>
                • 删除内容时高度自动缩小
            </div>
        </div>
    </div>
</body>
</html>
